{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, onMounted, computed } from 'vue';\nimport { NSpin, NGrid, NGi, NCard, NAlert, NDescriptions, NDescriptionsItem, NButton, NTag, NList, NListItem, NIcon, NEmpty, useMessage } from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport { CheckmarkCircleOutline, CloseCircleOutline, AppsOutline, AddOutline } from '@vicons/ionicons5';\nexport default {\n  __name: 'Dashboard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const userStore = useUserStore();\n    const message = useMessage();\n    const recentApps = ref([]);\n\n    // 用于高度同步的refs\n    const mainCard = ref(null);\n    const sideCards = ref(null);\n\n    // 安全相关数据\n    const securityScore = ref(75); // 安全评分 0-100\n\n    const securityItems = computed(() => [{\n      key: 'email_verified',\n      name: '邮箱验证',\n      status: userStore.user?.is_email_verified || false,\n      actionText: '去验证'\n    }, {\n      key: 'phone_verified',\n      name: '手机验证',\n      status: userStore.user?.is_phone_verified || false,\n      actionText: '去验证'\n    }, {\n      key: 'level2_verified',\n      name: '实名认证',\n      status: userStore.user?.level2_verified || false,\n      actionText: '去认证'\n    }, {\n      key: 'mfa_enabled',\n      name: '双因子认证',\n      status: userStore.user?.security_mfa_enabled || false,\n      actionText: '去开启'\n    }]);\n\n    // 检查是否为深色模式\n    const isDarkMode = computed(() => {\n      const themeMode = localStorage.getItem('theme') || 'system';\n      if (themeMode === 'system') {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      return themeMode === 'dark';\n    });\n    const formatDateTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      // Using toLocaleString for a more standard format, customize as needed\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).replace(/\\//g, '-');\n    };\n\n    // 安全相关方法\n    const getSecurityArcPath = score => {\n      // 将分数转换为弧度 (0-100 映射到 0-π)\n      const angle = score / 100 * Math.PI;\n      const x = 100 + 80 * Math.cos(Math.PI - angle);\n      const y = 100 - 80 * Math.sin(Math.PI - angle);\n      return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n    };\n    const getSecurityColor = score => {\n      if (score >= 80) return '#18a058'; // 绿色 - 安全\n      if (score >= 60) return '#f0a020'; // 橙色 - 一般\n      return '#d03050'; // 红色 - 危险\n    };\n    const getSecurityLevelType = score => {\n      if (score >= 80) return 'success';\n      if (score >= 60) return 'warning';\n      return 'error';\n    };\n    const getSecurityLevelText = score => {\n      if (score >= 80) return '安全';\n      if (score >= 60) return '一般';\n      return '危险';\n    };\n\n    // 紧凑型仪表盘弧线路径计算\n    const getCompactSecurityArcPath = score => {\n      // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n      const angle = score / 100 * Math.PI;\n      const x = 60 + 45 * Math.cos(Math.PI - angle);\n      const y = 65 - 45 * Math.sin(Math.PI - angle);\n      return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n    };\n    const handleSecurityAction = key => {\n      switch (key) {\n        case 'email_verified':\n          message.info('邮箱验证功能开发中');\n          break;\n        case 'phone_verified':\n          message.info('手机验证功能开发中');\n          break;\n        case 'level2_verified':\n          window.location.href = '/verification';\n          break;\n        case 'mfa_enabled':\n          window.location.href = '/security';\n          break;\n        default:\n          message.info('功能开发中');\n      }\n    };\n\n    // 计算安全评分\n    const calculateSecurityScore = () => {\n      const items = securityItems.value;\n      const completedItems = items.filter(item => item.status).length;\n      const score = Math.round(completedItems / items.length * 100);\n      securityScore.value = score;\n    };\n    const fetchDashboardData = async () => {\n      loading.value = true;\n      try {\n        const apiClient = getApiClient();\n        const response = await apiClient.get('/dashboard');\n        if (response.data && response.data.success) {\n          // 更新用户信息，使用后端返回的格式化数据\n          if (response.data.user) {\n            userStore.user = {\n              ...userStore.user,\n              ...response.data.user,\n              // 使用后端返回的格式化时间，如果没有则使用原始数据\n              createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n              lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n              last_login: response.data.user.last_login,\n              lastLoginIp: response.data.user.lastLoginIp,\n              level2_verified: response.data.user.level2_verified\n            };\n          }\n\n          // 更新应用列表\n          recentApps.value = response.data.recentApps || [];\n          console.log('仪表盘数据加载成功:', response.data);\n        }\n\n        // 计算安全评分\n        calculateSecurityScore();\n      } catch (error) {\n        console.error(\"Failed to fetch dashboard data:\", error);\n        message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 同步右侧卡片高度到左侧信息卡片高度\n    const syncCardHeights = () => {\n      if (mainCard.value && sideCards.value) {\n        // 获取左侧信息卡片的实际高度\n        const mainCardHeight = mainCard.value.offsetHeight;\n\n        // 设置右侧容器的高度等于左侧信息卡片的高度\n        sideCards.value.style.height = `${mainCardHeight}px`;\n        console.log('同步高度 - 左侧卡片:', mainCardHeight, 'px');\n      }\n    };\n    onMounted(() => {\n      // We need user data, if not present, maybe fetch it or rely on login flow\n      if (!userStore.user) {\n        // This case might happen on a page refresh, you might want to fetch user data\n        // For now, we assume user data is populated from login\n      }\n      fetchDashboardData();\n\n      // 等待DOM渲染完成后同步高度\n      setTimeout(() => {\n        syncCardHeights();\n      }, 300);\n\n      // 监听窗口大小变化，重新同步高度\n      window.addEventListener('resize', syncCardHeights);\n    });\n    const __returned__ = {\n      loading,\n      userStore,\n      message,\n      recentApps,\n      mainCard,\n      sideCards,\n      securityScore,\n      securityItems,\n      isDarkMode,\n      formatDateTime,\n      getSecurityArcPath,\n      getSecurityColor,\n      getSecurityLevelType,\n      getSecurityLevelText,\n      getCompactSecurityArcPath,\n      handleSecurityAction,\n      calculateSecurityScore,\n      fetchDashboardData,\n      syncCardHeights,\n      ref,\n      onMounted,\n      computed,\n      get NSpin() {\n        return NSpin;\n      },\n      get NGrid() {\n        return NGrid;\n      },\n      get NGi() {\n        return NGi;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NDescriptions() {\n        return NDescriptions;\n      },\n      get NDescriptionsItem() {\n        return NDescriptionsItem;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NList() {\n        return NList;\n      },\n      get NListItem() {\n        return NListItem;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NEmpty() {\n        return NEmpty;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get AppsOutline() {\n        return AppsOutline;\n      },\n      get AddOutline() {\n        return AddOutline;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "NSpin", "NGrid", "NGi", "NCard", "N<PERSON><PERSON><PERSON>", "NDescriptions", "NDescriptionsItem", "NButton", "NTag", "NList", "NListItem", "NIcon", "NEmpty", "useMessage", "useUserStore", "getApiClient", "CheckmarkCircleOutline", "CloseCircleOutline", "AppsOutline", "AddOutline", "loading", "userStore", "message", "recentApps", "mainCard", "sideCards", "securityScore", "securityItems", "key", "name", "status", "user", "is_email_verified", "actionText", "is_phone_verified", "level2_verified", "security_mfa_enabled", "isDarkMode", "themeMode", "localStorage", "getItem", "window", "matchMedia", "matches", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "getSecurityArcPath", "score", "angle", "Math", "PI", "x", "cos", "y", "sin", "getSecurityColor", "getSecurityLevelType", "getSecurityLevelText", "getCompactSecurityArcPath", "handleSecurityAction", "info", "location", "href", "calculateSecurityScore", "items", "value", "completedItems", "filter", "item", "length", "round", "fetchDashboardData", "apiClient", "response", "get", "data", "success", "createdAt", "registrationTime", "formatted", "lastLoginAt", "lastLoginTime", "last_login", "lastLoginIp", "console", "log", "error", "syncCardHeights", "mainCardHeight", "offsetHeight", "style", "height", "setTimeout", "addEventListener"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          欢迎使用空旷账户中心！目前处于内测阶段\n        </n-alert>\n\n        <!-- 完全重构的布局 -->\n        <div class=\"cards-container\">\n          <!-- 主要信息卡片 -->\n          <div class=\"main-card\" ref=\"mainCard\">\n            <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions label-placement=\"top\" :column=\"2\">\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\" size=\"small\">\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n              </n-descriptions>\n              <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                更改密码\n              </n-button>\n            </n-card>\n          </div>\n\n          <!-- 右侧两个卡片容器 -->\n          <div class=\"side-cards\" ref=\"sideCards\">\n            <!-- 服务卡片 -->\n            <div class=\"side-card\">\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"compact-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item\">\n                      <div class=\"service-dot\"></div>\n                      <span>{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"empty-state\">\n                    <n-icon size=\"20\" color=\"#d0d0d0\"><apps-outline /></n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n\n            <!-- 安全指数卡片 -->\n            <div class=\"side-card\">\n              <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <template #header>\n                  <div class=\"card-header\">\n                    <span>账户安全指数</span>\n                    <n-button text type=\"primary\" size=\"small\" @click=\"$router.push('/security')\">\n                      <template #icon><n-icon><add-outline /></n-icon></template>\n                    </n-button>\n                  </div>\n                </template>\n                <div class=\"card-content compact-content\">\n                  <div class=\"security-display\">\n                    <div class=\"score-big\" :style=\"{ color: getSecurityColor(securityScore) }\">\n                      {{ securityScore }}\n                    </div>\n                    <n-tag :type=\"getSecurityLevelType(securityScore)\" :bordered=\"false\" size=\"small\">\n                      {{ getSecurityLevelText(securityScore) }}\n                    </n-tag>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline,\n  AddOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 用于高度同步的refs\nconst mainCard = ref(null);\nconst sideCards = ref(null);\n\n\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 同步右侧卡片高度到左侧信息卡片高度\nconst syncCardHeights = () => {\n  if (mainCard.value && sideCards.value) {\n    // 获取左侧信息卡片的实际高度\n    const mainCardHeight = mainCard.value.offsetHeight;\n\n    // 设置右侧容器的高度等于左侧信息卡片的高度\n    sideCards.value.style.height = `${mainCardHeight}px`;\n\n    console.log('同步高度 - 左侧卡片:', mainCardHeight, 'px');\n  }\n};\n\nonMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n\n  // 等待DOM渲染完成后同步高度\n  setTimeout(() => {\n    syncCardHeights();\n  }, 300);\n\n  // 监听窗口大小变化，重新同步高度\n  window.addEventListener('resize', syncCardHeights);\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n/* 重构后的布局样式 */\n.dashboard-main-layout {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n/* 完全重构的布局 - 精确控制高度 */\n.cards-container {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n.main-card {\n  flex: 2; /* 占2/3宽度 */\n}\n\n.side-cards {\n  flex: 1; /* 占1/3宽度 */\n  display: flex;\n  flex-direction: column;\n  gap: 16px; /* 增加卡片间距防止重叠 */\n  /* 高度将由JavaScript动态设置 */\n}\n\n.side-card {\n  /* 让两个卡片等高 */\n  flex: 1; /* 平分右侧区域高度 */\n  overflow: hidden; /* 防止内容溢出 */\n  min-height: 150px; /* 确保每个卡片有最小高度 */\n  position: relative; /* 确保正常的文档流 */\n}\n\n/* 卡片基础样式 */\n.main-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n}\n\n.side-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: 100%; /* 让卡片填充整个容器高度 */\n  display: flex;\n  flex-direction: column;\n}\n\n/* 确保卡片内容区域也是flex布局 */\n.side-card .n-card .n-card__content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 0; /* 移除默认padding */\n}\n\n/* 确保卡片头部不占用过多空间 */\n.side-card .n-card .n-card__header {\n  padding: 12px 16px 8px 16px; /* 减少头部padding */\n  flex-shrink: 0; /* 不允许收缩 */\n}\n\n/* 紧凑的卡片内容样式 */\n.compact-content {\n  padding: 16px 0; /* 适当的上下padding */\n  display: flex;\n  flex-direction: column;\n  justify-content: center; /* 垂直居中 */\n  align-items: center; /* 水平居中 */\n  overflow: hidden; /* 防止内容溢出 */\n  flex: 1; /* 填充整个卡片内容区域 */\n  height: 100%; /* 确保占满整个卡片高度 */\n  min-height: 120px; /* 确保有足够空间居中 */\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n  padding: 0; /* 移除额外padding */\n  margin: 0; /* 移除额外margin */\n}\n\n/* 服务列表样式 */\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  width: 100%;\n}\n\n.service-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 卡片内容样式 */\n.card-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  padding: 16px 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n/* 安全指数样式 */\n.security-display {\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  flex: 1;\n  height: 100%;\n  width: 100%;\n  padding: 0; /* 移除padding，让内容完全居中 */\n}\n\n.score-big {\n  font-size: 36px;\n  font-weight: bold;\n  line-height: 1;\n  margin: 0;\n  text-align: center;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n\n\n.service-card-wrapper,\n.security-card-wrapper {\n  flex: 1; /* 两个卡片平分右侧高度 */\n  display: flex;\n  flex-direction: column;\n}\n\n.service-card-new,\n.security-card-new {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  /* 移除高度限制，让它们平分父容器高度 */\n}\n\n/* 服务卡片内容样式 */\n.service-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.service-list-new {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.service-item-new {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot-new {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n.service-name-new {\n  font-size: 14px;\n  color: var(--text-color);\n}\n\n.no-services-new {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 安全指数卡片样式 */\n.security-header-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n.security-action-btn-new {\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.security-action-btn-new:hover {\n  opacity: 1;\n}\n\n.security-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 16px;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.security-score-new {\n  text-align: center;\n}\n\n.score-number-new {\n  font-size: clamp(28px, 6vw, 48px);\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: 8px;\n}\n\n.score-label-new {\n  margin-bottom: 16px;\n}\n\n.security-guide-new {\n  text-align: center;\n}\n\n.guide-text-new {\n  font-size: 12px;\n  color: var(--text-color-3);\n  display: block;\n  margin-bottom: 8px;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 垂直并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n  flex: 1;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  flex-direction: column;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩，适应水平布局 */\n}\n\n.services-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 8px 0;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px; /* 增加项目间距 */\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 12px; /* 增加内边距 */\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.service-item-compact:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.service-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  color: var(--n-text-color-3);\n  font-size: 13px;\n  padding: 20px;\n}\n\n.more-services {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 8px;\n  padding: 4px 8px;\n  background-color: var(--n-color-target);\n  border-radius: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  min-height: 240px;\n  overflow: visible;\n}\n\n.security-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.security-action-btn {\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.security-action-btn:hover {\n  opacity: 1;\n}\n\n.security-index-display {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  flex: 1;\n  padding: 16px;\n  justify-content: flex-start;\n}\n\n.security-score-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n}\n\n.score-number {\n  font-size: clamp(32px, 8vw, 48px);\n  font-weight: 700;\n  line-height: 1;\n  transition: color 0.3s ease;\n}\n\n.score-label {\n  display: flex;\n  justify-content: center;\n}\n\n.security-guide {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n  text-align: center;\n}\n\n.guide-text {\n  font-size: 13px;\n  color: var(--n-text-color-2);\n  line-height: 1.4;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards {\n  gap: 24px;\n  padding: 8px;\n}\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;AAoGA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SACEC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,UAAS,QACJ,UAAU;AACjB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,WAAW,EACXC,UAAS,QACJ,mBAAmB;;;;;;;IAE1B,MAAMC,OAAO,GAAGvB,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMwB,SAAS,GAAGP,YAAY,CAAC,CAAC;IAChC,MAAMQ,OAAO,GAAGT,UAAU,CAAC,CAAC;IAE5B,MAAMU,UAAU,GAAG1B,GAAG,CAAC,EAAE,CAAC;;IAE1B;IACA,MAAM2B,QAAQ,GAAG3B,GAAG,CAAC,IAAI,CAAC;IAC1B,MAAM4B,SAAS,GAAG5B,GAAG,CAAC,IAAI,CAAC;;IAI3B;IACA,MAAM6B,aAAa,GAAG7B,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE/B,MAAM8B,aAAa,GAAG5B,QAAQ,CAAC,MAAM,CACnC;MACE6B,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAET,SAAS,CAACU,IAAI,EAAEC,iBAAiB,IAAI,KAAK;MAClDC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAET,SAAS,CAACU,IAAI,EAAEG,iBAAiB,IAAI,KAAK;MAClDD,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAET,SAAS,CAACU,IAAI,EAAEI,eAAe,IAAI,KAAK;MAChDF,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAET,SAAS,CAACU,IAAI,EAAEK,oBAAoB,IAAI,KAAK;MACrDH,UAAU,EAAE;IACd,EACD,CAAC;;IAEF;IACA,MAAMI,UAAU,GAAGtC,QAAQ,CAAC,MAAM;MAChC,MAAMuC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ;MAC3D,IAAIF,SAAS,KAAK,QAAQ,EAAE;QAC1B,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAClE;MACA,OAAOL,SAAS,KAAK,MAAM;IAC7B,CAAC,CAAC;IAEF,MAAMM,cAAc,GAAIC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC;MACA,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACxB,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA,MAAMC,KAAK,GAAID,KAAK,GAAG,GAAG,GAAIE,IAAI,CAACC,EAAE;MACrC,MAAMC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAC9C,MAAMK,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAE9C,OAAO,0BAA0BG,CAAC,IAAIE,CAAC,EAAE;IAC3C,CAAC;IAED,MAAME,gBAAgB,GAAIR,KAAK,IAAK;MAClC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,OAAO,SAAS,CAAC,CAAC;IACpB,CAAC;IAED,MAAMS,oBAAoB,GAAIT,KAAK,IAAK;MACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;MACjC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;MACjC,OAAO,OAAO;IAChB,CAAC;IAED,MAAMU,oBAAoB,GAAIV,KAAK,IAAK;MACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAMW,yBAAyB,GAAIX,KAAK,IAAK;MAC3C;MACA,MAAMC,KAAK,GAAID,KAAK,GAAG,GAAG,GAAIE,IAAI,CAACC,EAAE;MACrC,MAAMC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAC7C,MAAMK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAE7C,OAAO,yBAAyBG,CAAC,IAAIE,CAAC,EAAE;IAC1C,CAAC;IAED,MAAMM,oBAAoB,GAAI1C,GAAG,IAAK;MACpC,QAAQA,GAAG;QACT,KAAK,gBAAgB;UACnBN,OAAO,CAACiD,IAAI,CAAC,WAAW,CAAC;UACzB;QACF,KAAK,gBAAgB;UACnBjD,OAAO,CAACiD,IAAI,CAAC,WAAW,CAAC;UACzB;QACF,KAAK,iBAAiB;UACpB9B,MAAM,CAAC+B,QAAQ,CAACC,IAAI,GAAG,eAAe;UACtC;QACF,KAAK,aAAa;UAChBhC,MAAM,CAAC+B,QAAQ,CAACC,IAAI,GAAG,WAAW;UAClC;QACF;UACEnD,OAAO,CAACiD,IAAI,CAAC,OAAO,CAAC;MACzB;IACF,CAAC;;IAED;IACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;MACnC,MAAMC,KAAK,GAAGhD,aAAa,CAACiD,KAAK;MACjC,MAAMC,cAAc,GAAGF,KAAK,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjD,MAAM,CAAC,CAACkD,MAAM;MAC/D,MAAMtB,KAAK,GAAGE,IAAI,CAACqB,KAAK,CAAEJ,cAAc,GAAGF,KAAK,CAACK,MAAM,GAAI,GAAG,CAAC;MAC/DtD,aAAa,CAACkD,KAAK,GAAGlB,KAAK;IAC7B,CAAC;IAED,MAAMwB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC9D,OAAO,CAACwD,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMO,SAAS,GAAGpE,YAAY,CAAC,CAAC;QAChC,MAAMqE,QAAQ,GAAG,MAAMD,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;QAElD,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UAC1C;UACA,IAAIH,QAAQ,CAACE,IAAI,CAACvD,IAAI,EAAE;YACtBV,SAAS,CAACU,IAAI,GAAG;cACf,GAAGV,SAAS,CAACU,IAAI;cACjB,GAAGqD,QAAQ,CAACE,IAAI,CAACvD,IAAI;cACrB;cACAyD,SAAS,EAAEJ,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC0D,gBAAgB,EAAEC,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAACyD,SAAS;cACzFG,WAAW,EAAEP,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC6D,aAAa,EAAEF,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC4D,WAAW;cAC1FE,UAAU,EAAET,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC8D,UAAU;cACzCC,WAAW,EAAEV,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC+D,WAAW;cAC3C3D,eAAe,EAAEiD,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAACI;YACtC,CAAC;UACH;;UAEA;UACAZ,UAAU,CAACqD,KAAK,GAAGQ,QAAQ,CAACE,IAAI,CAAC/D,UAAU,IAAI,EAAE;UAEjDwE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC1C;;QAEA;QACAZ,sBAAsB,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD3E,OAAO,CAAC2E,KAAK,CAAC,aAAa,IAAIA,KAAK,CAACb,QAAQ,EAAEE,IAAI,EAAEhE,OAAO,IAAI2E,KAAK,CAAC3E,OAAO,CAAC,CAAC;MACjF,CAAC,SAAS;QACRF,OAAO,CAACwD,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI1E,QAAQ,CAACoD,KAAK,IAAInD,SAAS,CAACmD,KAAK,EAAE;QACrC;QACA,MAAMuB,cAAc,GAAG3E,QAAQ,CAACoD,KAAK,CAACwB,YAAY;;QAElD;QACA3E,SAAS,CAACmD,KAAK,CAACyB,KAAK,CAACC,MAAM,GAAG,GAAGH,cAAc,IAAI;QAEpDJ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,cAAc,EAAE,IAAI,CAAC;MACnD;IACF,CAAC;IAEDrG,SAAS,CAAC,MAAM;MACd;MACA,IAAI,CAACuB,SAAS,CAACU,IAAI,EAAE;QACnB;QACA;MAAA;MAEFmD,kBAAkB,CAAC,CAAC;;MAEpB;MACAqB,UAAU,CAAC,MAAM;QACfL,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;;MAEP;MACAzD,MAAM,CAAC+D,gBAAgB,CAAC,QAAQ,EAAEN,eAAe,CAAC;IACpD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}