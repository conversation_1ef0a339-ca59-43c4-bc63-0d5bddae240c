{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"cards-container\"\n};\nconst _hoisted_5 = {\n  class: \"main-card\",\n  ref: \"mainCard\"\n};\nconst _hoisted_6 = {\n  class: \"email-item\"\n};\nconst _hoisted_7 = {\n  class: \"side-cards\",\n  ref: \"sideCards\"\n};\nconst _hoisted_8 = {\n  class: \"side-card\"\n};\nconst _hoisted_9 = {\n  class: \"compact-content\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"service-list\"\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"empty-state\"\n};\nconst _hoisted_12 = {\n  class: \"side-card\"\n};\nconst _hoisted_13 = {\n  class: \"card-header\"\n};\nconst _hoisted_14 = {\n  class: \"card-content compact-content\"\n};\nconst _hoisted_15 = {\n  class: \"security-display\"\n};\nexport function render(_ctx, _cache) {\n  const _component_n_alert = _resolveComponent(\"n-alert\");\n  const _component_n_descriptions_item = _resolveComponent(\"n-descriptions-item\");\n  const _component_n_tag = _resolveComponent(\"n-tag\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_descriptions = _resolveComponent(\"n-descriptions\");\n  const _component_n_card = _resolveComponent(\"n-card\");\n  const _component_apps_outline = _resolveComponent(\"apps-outline\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_add_outline = _resolveComponent(\"add-outline\");\n  const _component_n_spin = _resolveComponent(\"n-spin\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_n_spin, {\n    show: _ctx.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString(_ctx.userStore.user?.username), 1 /* TEXT */), _createVNode(_component_n_alert, {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 欢迎使用空旷账户中心！目前处于内测阶段 \")])),\n      _: 1 /* STABLE */,\n      __: [2]\n    }), _createCommentVNode(\" 完全重构的布局 \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 主要信息卡片 \"), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_n_card, {\n      bordered: false,\n      \"theme-overrides\": {\n        color: _ctx.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_descriptions, {\n        \"label-placement\": \"top\",\n        column: 2\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_descriptions_item, {\n          label: \"ID\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.userStore.user?.id), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"实名状态\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_tag, {\n            bordered: false,\n            type: _ctx.userStore.user?.level2_verified ? 'success' : 'warning',\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"注册时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.formatDateTime(_ctx.userStore.user?.createdAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"最后登录时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.formatDateTime(_ctx.userStore.user?.last_login || _ctx.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"最后登录 IP\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"用户状态\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_tag, {\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"正常\")])),\n            _: 1 /* STABLE */,\n            __: [3]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_descriptions_item, {\n          label: \"绑定邮箱\",\n          span: 2\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", null, _toDisplayString(_ctx.userStore.user?.email), 1 /* TEXT */), _createVNode(_component_n_button, {\n            text: \"\",\n            type: \"primary\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"换绑\")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_n_button, {\n        type: \"primary\",\n        ghost: \"\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n        style: {\n          \"margin-top\": \"16px\"\n        }\n      }, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 更改密码 \")])),\n        _: 1 /* STABLE */,\n        __: [5]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])], 512 /* NEED_PATCH */), _createCommentVNode(\" 右侧两个卡片容器 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 服务卡片 \"), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_n_card, {\n      title: \"可使用空旷账户登录的服务\",\n      bordered: false,\n      \"theme-overrides\": {\n        color: _ctx.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_ctx.recentApps && _ctx.recentApps.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.recentApps.slice(0, 3), app => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: app.id,\n          class: \"service-item\"\n        }, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n          class: \"service-dot\"\n        }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(app.name), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_n_icon, {\n        size: \"20\",\n        color: \"#d0d0d0\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_apps_outline)]),\n        _: 1 /* STABLE */\n      }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"暂无服务\", -1 /* CACHED */))]))])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])]), _createCommentVNode(\" 安全指数卡片 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_n_card, {\n      bordered: false,\n      \"theme-overrides\": {\n        color: _ctx.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"账户安全指数\", -1 /* CACHED */)), _createVNode(_component_n_button, {\n        text: \"\",\n        type: \"primary\",\n        size: \"small\",\n        onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/security'))\n      }, {\n        icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_add_outline)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })])]),\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n        class: \"score-big\",\n        style: _normalizeStyle({\n          color: _ctx.getSecurityColor(_ctx.securityScore)\n        })\n      }, _toDisplayString(_ctx.securityScore), 5 /* TEXT, STYLE */), _createVNode(_component_n_tag, {\n        type: _ctx.getSecurityLevelType(_ctx.securityScore),\n        bordered: false,\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.getSecurityLevelText(_ctx.securityScore)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"])])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])])], 512 /* NEED_PATCH */)])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_n_spin", "show", "_ctx", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "_component_n_alert", "title", "type", "bordered", "_cache", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_component_n_card", "color", "isDarkMode", "_component_n_descriptions", "column", "_component_n_descriptions_item", "label", "id", "_component_n_tag", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "span", "_hoisted_6", "email", "_component_n_button", "text", "ghost", "onClick", "$event", "$router", "push", "style", "_hoisted_7", "_hoisted_8", "_hoisted_9", "recentApps", "length", "_hoisted_10", "_Fragment", "_renderList", "slice", "app", "key", "name", "_hoisted_11", "_component_n_icon", "_component_apps_outline", "_hoisted_12", "header", "_withCtx", "_hoisted_13", "icon", "_component_add_outline", "_hoisted_14", "_hoisted_15", "_normalizeStyle", "getSecurityColor", "securityScore", "getSecurityLevelType", "getSecurityLevelText"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          欢迎使用空旷账户中心！目前处于内测阶段\n        </n-alert>\n\n        <!-- 完全重构的布局 -->\n        <div class=\"cards-container\">\n          <!-- 主要信息卡片 -->\n          <div class=\"main-card\" ref=\"mainCard\">\n            <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions label-placement=\"top\" :column=\"2\">\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\" size=\"small\">\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n              </n-descriptions>\n              <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                更改密码\n              </n-button>\n            </n-card>\n          </div>\n\n          <!-- 右侧两个卡片容器 -->\n          <div class=\"side-cards\" ref=\"sideCards\">\n            <!-- 服务卡片 -->\n            <div class=\"side-card\">\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"compact-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item\">\n                      <div class=\"service-dot\"></div>\n                      <span>{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"empty-state\">\n                    <n-icon size=\"20\" color=\"#d0d0d0\"><apps-outline /></n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n\n            <!-- 安全指数卡片 -->\n            <div class=\"side-card\">\n              <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <template #header>\n                  <div class=\"card-header\">\n                    <span>账户安全指数</span>\n                    <n-button text type=\"primary\" size=\"small\" @click=\"$router.push('/security')\">\n                      <template #icon><n-icon><add-outline /></n-icon></template>\n                    </n-button>\n                  </div>\n                </template>\n                <div class=\"card-content compact-content\">\n                  <div class=\"security-display\">\n                    <div class=\"score-big\" :style=\"{ color: getSecurityColor(securityScore) }\">\n                      {{ securityScore }}\n                    </div>\n                    <n-tag :type=\"getSecurityLevelType(securityScore)\" :bordered=\"false\" size=\"small\">\n                      {{ getSecurityLevelText(securityScore) }}\n                    </n-tag>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline,\n  AddOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 用于高度同步的refs\nconst mainCard = ref(null);\nconst sideCards = ref(null);\n\n\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n;\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 同步右侧卡片高度到左侧信息卡片高度\nconst syncCardHeights = () => {\n  if (mainCard.value && sideCards.value) {\n    // 获取左侧信息卡片的实际高度\n    const mainCardHeight = mainCard.value.offsetHeight;\n\n    // 设置右侧容器的高度等于左侧信息卡片的高度\n    sideCards.value.style.height = `${mainCardHeight}px`;\n\n    console.log('同步高度 - 左侧卡片:', mainCardHeight, 'px');\n  }\n};\n\nonMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n\n  // 等待DOM渲染完成后同步高度\n  setTimeout(() => {\n    syncCardHeights();\n  }, 300);\n\n  // 监听窗口大小变化，重新同步高度\n  window.addEventListener('resize', syncCardHeights);\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n/* 重构后的布局样式 */\n.dashboard-main-layout {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n/* 完全重构的布局 - 精确控制高度 */\n.cards-container {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n.main-card {\n  flex: 2; /* 占2/3宽度 */\n}\n\n.side-cards {\n  flex: 1; /* 占1/3宽度 */\n  display: flex;\n  flex-direction: column;\n  gap: 12px; /* 减少卡片间距 */\n  /* 高度将由JavaScript动态设置 */\n}\n\n.side-card {\n  /* 让两个卡片等高 */\n  flex: 1; /* 平分右侧区域高度 */\n  overflow: hidden; /* 防止内容溢出 */\n}\n\n/* 卡片基础样式 */\n.main-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n}\n\n.side-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: 100%; /* 让卡片填充整个容器高度 */\n  display: flex;\n  flex-direction: column;\n}\n\n/* 确保卡片内容区域也是flex布局 */\n.side-card .n-card .n-card__content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 0; /* 移除默认padding */\n}\n\n/* 确保卡片头部不占用过多空间 */\n.side-card .n-card .n-card__header {\n  padding: 12px 16px 8px 16px; /* 减少头部padding */\n  flex-shrink: 0; /* 不允许收缩 */\n}\n\n/* 紧凑的卡片内容样式 */\n.compact-content {\n  padding: 0; /* 移除padding让内容完全居中 */\n  display: flex;\n  flex-direction: column;\n  justify-content: center; /* 垂直居中 */\n  align-items: center; /* 水平居中 */\n  overflow: hidden; /* 防止内容溢出 */\n  flex: 1; /* 填充整个卡片内容区域 */\n  height: 100%; /* 确保占满整个卡片高度 */\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n  padding: 0; /* 移除额外padding */\n  margin: 0; /* 移除额外margin */\n}\n\n/* 服务列表样式 */\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  width: 100%;\n}\n\n.service-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 卡片内容样式 */\n.card-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  padding: 16px 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n/* 安全指数样式 */\n.security-display {\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  flex: 1;\n  height: 100%;\n  padding: 16px 0;\n}\n\n.score-big {\n  font-size: 32px;\n  font-weight: bold;\n  line-height: 1;\n  margin: 0;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n\n\n.service-card-wrapper,\n.security-card-wrapper {\n  flex: 1; /* 两个卡片平分右侧高度 */\n  display: flex;\n  flex-direction: column;\n}\n\n.service-card-new,\n.security-card-new {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  /* 移除高度限制，让它们平分父容器高度 */\n}\n\n/* 服务卡片内容样式 */\n.service-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.service-list-new {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.service-item-new {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot-new {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n.service-name-new {\n  font-size: 14px;\n  color: var(--text-color);\n}\n\n.no-services-new {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 安全指数卡片样式 */\n.security-header-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n.security-action-btn-new {\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.security-action-btn-new:hover {\n  opacity: 1;\n}\n\n.security-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 16px;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.security-score-new {\n  text-align: center;\n}\n\n.score-number-new {\n  font-size: clamp(28px, 6vw, 48px);\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: 8px;\n}\n\n.score-label-new {\n  margin-bottom: 16px;\n}\n\n.security-guide-new {\n  text-align: center;\n}\n\n.guide-text-new {\n  font-size: 12px;\n  color: var(--text-color-3);\n  display: block;\n  margin-bottom: 8px;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 垂直并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n  flex: 1;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  flex-direction: column;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩，适应水平布局 */\n}\n\n.services-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 8px 0;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px; /* 增加项目间距 */\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 12px; /* 增加内边距 */\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.service-item-compact:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.service-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  color: var(--n-text-color-3);\n  font-size: 13px;\n  padding: 20px;\n}\n\n.more-services {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 8px;\n  padding: 4px 8px;\n  background-color: var(--n-color-target);\n  border-radius: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  min-height: 240px;\n  overflow: visible;\n}\n\n.security-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.security-action-btn {\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.security-action-btn:hover {\n  opacity: 1;\n}\n\n.security-index-display {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  flex: 1;\n  padding: 16px;\n  justify-content: flex-start;\n}\n\n.security-score-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n}\n\n.score-number {\n  font-size: clamp(32px, 8vw, 48px);\n  font-weight: 700;\n  line-height: 1;\n  transition: color 0.3s ease;\n}\n\n.score-label {\n  display: flex;\n  justify-content: center;\n}\n\n.security-guide {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n  text-align: center;\n}\n\n.guide-text {\n  font-size: 13px;\n  color: var(--n-text-color-2);\n  line-height: 1.4;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards {\n  gap: 24px;\n  padding: 8px;\n}\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAOpBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC,WAAW;EAACC,GAAG,EAAC;;;EAwBdD,KAAK,EAAC;AAAY;;EAa1BA,KAAK,EAAC,YAAY;EAACC,GAAG,EAAC;;;EAErBD,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAiB;;;EACsBA,KAAK,EAAC;;;;EAM1CA,KAAK,EAAC;;;EASnBA,KAAK,EAAC;AAAW;;EAGXA,KAAK,EAAC;AAAa;;EAOrBA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAAkB;;;;;;;;;;;;uBAhF7CE,mBAAA,CA+FM,OA/FNC,UA+FM,GA9FJC,YAAA,CA6FSC,iBAAA;IA7FAC,IAAI,EAAEC,IAAA,CAAAC;EAAO;sBACpB,MA2FM,CA3FNC,mBAAA,CA2FM,OA3FNC,UA2FM,GA1FJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGL,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDX,YAAA,CAEUY,kBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEnB,KAAK,EAAC;;wBAAa,MAEpEoB,MAAA,QAAAA,MAAA,O,iBAFoE,uBAEpE,E;;;QAEAC,mBAAA,aAAgB,EAChBZ,mBAAA,CAkFM,OAlFNa,UAkFM,GAjFJD,mBAAA,YAAe,EACfZ,mBAAA,CAkCM,OAlCNc,UAkCM,GAjCJnB,YAAA,CAgCSoB,iBAAA;MAhCAL,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAM,KAAA,EAAWlB,IAAA,CAAAmB,UAAU;MAAA;;wBAC7D,MA2BiB,CA3BjBtB,YAAA,CA2BiBuB,yBAAA;QA3BD,iBAAe,EAAC,KAAK;QAAEC,MAAM,EAAE;;0BAC7C,MAEsB,CAFtBxB,YAAA,CAEsByB,8BAAA;UAFDC,KAAK,EAAC;QAAI;4BAC7B,MAAwB,C,kCAArBvB,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEiB,EAAE,iB;;YAEvB3B,YAAA,CAIsByB,8BAAA;UAJDC,KAAK,EAAC;QAAM;4BAC/B,MAEQ,CAFR1B,YAAA,CAEQ4B,gBAAA;YAFAb,QAAQ,EAAE,KAAK;YAAGD,IAAI,EAAEX,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEmB,eAAe;YAA0BC,IAAI,EAAC;;8BAC5F,MAAqD,C,kCAAlD3B,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEmB,eAAe,iC;;;;YAGtC7B,YAAA,CAEsByB,8BAAA;UAFDC,KAAK,EAAC;QAAM;4BAC/B,MAA+C,C,kCAA5CvB,IAAA,CAAA4B,cAAc,CAAC5B,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEsB,SAAS,kB;;YAE7ChC,YAAA,CAEsByB,8BAAA;UAFDC,KAAK,EAAC;QAAQ;4BACjC,MAA+E,C,kCAA5EvB,IAAA,CAAA4B,cAAc,CAAC5B,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEuB,UAAU,IAAI9B,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEwB,WAAW,kB;;YAE7ElC,YAAA,CAEsByB,8BAAA;UAFDC,KAAK,EAAC;QAAS;4BAClC,MAAyC,C,kCAAtCvB,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAEyB,WAAW,yB;;YAEhCnC,YAAA,CAEsByB,8BAAA;UAFDC,KAAK,EAAC;QAAM;4BAC/B,MAA+D,CAA/D1B,YAAA,CAA+D4B,gBAAA;YAAvDb,QAAQ,EAAE,KAAK;YAAED,IAAI,EAAC,SAAS;YAACgB,IAAI,EAAC;;8BAAQ,MAAEd,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;YAEzDhB,YAAA,CAKsByB,8BAAA;UALDC,KAAK,EAAC,MAAM;UAAEU,IAAI,EAAE;;4BACvC,MAGM,CAHN/B,mBAAA,CAGM,OAHNgC,UAGM,GAFJhC,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BL,IAAA,CAAAM,SAAS,CAACC,IAAI,EAAE4B,KAAK,kBAC9BtC,YAAA,CAAwDuC,mBAAA;YAA9CC,IAAI,EAAJ,EAAI;YAAC1B,IAAI,EAAC,SAAS;YAACgB,IAAI,EAAC;;8BAAQ,MAAEd,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;UAInDhB,YAAA,CAEWuC,mBAAA;QAFDzB,IAAI,EAAC,SAAS;QAAC2B,KAAK,EAAL,EAAK;QAAEC,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAExC,IAAA,CAAAyC,OAAO,CAACC,IAAI;QAAeC,KAAyB,EAAzB;UAAA;QAAA;;0BAA0B,MAE5F9B,MAAA,QAAAA,MAAA,O,iBAF4F,QAE5F,E;;;;;oEAIJC,mBAAA,cAAiB,EACjBZ,mBAAA,CA0CM,OA1CN0C,UA0CM,GAzCJ9B,mBAAA,UAAa,EACbZ,mBAAA,CAeM,OAfN2C,UAeM,GAdJhD,YAAA,CAaSoB,iBAAA;MAbDP,KAAK,EAAC,cAAc;MAAEE,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAM,KAAA,EAAWlB,IAAA,CAAAmB,UAAU;MAAA;;wBAClF,MAWM,CAXNjB,mBAAA,CAWM,OAXN4C,UAWM,GAVO9C,IAAA,CAAA+C,UAAU,IAAI/C,IAAA,CAAA+C,UAAU,CAACC,MAAM,Q,cAA1CrD,mBAAA,CAKM,OALNsD,WAKM,I,kBAJJtD,mBAAA,CAGMuD,SAAA,QAAAC,WAAA,CAHanD,IAAA,CAAA+C,UAAU,CAACK,KAAK,QAAvBC,GAAG;6BAAf1D,mBAAA,CAGM;UAHsC2D,GAAG,EAAED,GAAG,CAAC7B,EAAE;UAAE/B,KAAK,EAAC;sCAC7DS,mBAAA,CAA+B;UAA1BT,KAAK,EAAC;QAAa,4BACxBS,mBAAA,CAA2B,cAAAG,gBAAA,CAAlBgD,GAAG,CAACE,IAAI,iB;yDAGrB5D,mBAAA,CAGM,OAHN6D,WAGM,GAFJ3D,YAAA,CAA2D4D,iBAAA;QAAnD9B,IAAI,EAAC,IAAI;QAACT,KAAK,EAAC;;0BAAU,MAAgB,CAAhBrB,YAAA,CAAgB6D,uBAAA,E;;oCAClDxD,mBAAA,CAAiB,cAAX,MAAI,oB;;8CAMlBY,mBAAA,YAAe,EACfZ,mBAAA,CAqBM,OArBNyD,WAqBM,GApBJ9D,YAAA,CAmBSoB,iBAAA;MAnBAL,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAM,KAAA,EAAWlB,IAAA,CAAAmB,UAAU;MAAA;;MAClDyC,MAAM,EAAAC,QAAA,CACf,MAKM,CALN3D,mBAAA,CAKM,OALN4D,WAKM,G,0BAJJ5D,mBAAA,CAAmB,cAAb,QAAM,qBACZL,YAAA,CAEWuC,mBAAA;QAFDC,IAAI,EAAJ,EAAI;QAAC1B,IAAI,EAAC,SAAS;QAACgB,IAAI,EAAC,OAAO;QAAEY,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAExC,IAAA,CAAAyC,OAAO,CAACC,IAAI;;QAClDqB,IAAI,EAAAF,QAAA,CAAC,MAAgC,CAAhChE,YAAA,CAAgC4D,iBAAA;4BAAxB,MAAe,CAAf5D,YAAA,CAAemE,sBAAA,E;;;;;wBAI7C,MASM,CATN9D,mBAAA,CASM,OATN+D,WASM,GARJ/D,mBAAA,CAOM,OAPNgE,WAOM,GANJhE,mBAAA,CAEM;QAFDT,KAAK,EAAC,WAAW;QAAEkD,KAAK,EAAAwB,eAAA;UAAAjD,KAAA,EAAWlB,IAAA,CAAAoE,gBAAgB,CAACpE,IAAA,CAAAqE,aAAa;QAAA;0BACjErE,IAAA,CAAAqE,aAAa,yBAElBxE,YAAA,CAEQ4B,gBAAA;QAFAd,IAAI,EAAEX,IAAA,CAAAsE,oBAAoB,CAACtE,IAAA,CAAAqE,aAAa;QAAIzD,QAAQ,EAAE,KAAK;QAAEe,IAAI,EAAC;;0BACxE,MAAyC,C,kCAAtC3B,IAAA,CAAAuE,oBAAoB,CAACvE,IAAA,CAAAqE,aAAa,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}