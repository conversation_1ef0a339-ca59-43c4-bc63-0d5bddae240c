<template>
  <div class="dashboard-container">
    <n-spin :show="loading">
      <div class="dashboard-content">
        <h2 class="welcome-title">你好, {{ userStore.user?.username }}</h2>

        <n-alert title="通知" type="info" :bordered="true" class="info-alert">
          欢迎使用空旷账户中心！目前处于内测阶段
        </n-alert>

        <!-- 完全重构的布局 -->
        <div class="cards-container">
          <!-- 主要信息卡片 -->
          <div class="main-card" ref="mainCard">
            <n-card :bordered="false" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
              <n-descriptions label-placement="top" :column="2">
                <n-descriptions-item label="ID">
                  {{ userStore.user?.id }}
                </n-descriptions-item>
                <n-descriptions-item label="实名状态">
                  <n-tag :bordered="false" :type="userStore.user?.level2_verified ? 'success' : 'warning'" size="small">
                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="注册时间">
                  {{ formatDateTime(userStore.user?.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录时间">
                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录 IP">
                  {{ userStore.user?.lastLoginIp || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="用户状态">
                  <n-tag :bordered="false" type="success" size="small">正常</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="绑定邮箱" :span="2">
                  <div class="email-item">
                    <span>{{ userStore.user?.email }}</span>
                    <n-button text type="primary" size="small">换绑</n-button>
                  </div>
                </n-descriptions-item>
              </n-descriptions>
              <n-button type="primary" ghost @click="$router.push('/security')" style="margin-top: 16px;">
                更改密码
              </n-button>
            </n-card>
          </div>

          <!-- 右侧两个卡片容器 -->
          <div class="side-cards" ref="sideCards">
            <!-- 服务卡片 -->
            <div class="side-card">
              <n-card title="可使用空旷账户登录的服务" :bordered="false" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <div class="compact-content">
                  <div v-if="recentApps && recentApps.length > 0" class="service-list">
                    <div v-for="app in recentApps.slice(0, 3)" :key="app.id" class="service-item">
                      <div class="service-dot"></div>
                      <span>{{ app.name }}</span>
                    </div>
                  </div>
                  <div v-else class="empty-state">
                    <n-icon size="20" color="#d0d0d0"><apps-outline /></n-icon>
                    <span>暂无服务</span>
                  </div>
                </div>
              </n-card>
            </div>

            <!-- 安全指数卡片 -->
            <div class="side-card">
              <n-card :bordered="false" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <template #header>
                  <div class="card-header">
                    <span>账户安全指数</span>
                    <n-button text type="primary" size="small" @click="$router.push('/security')">
                      <template #icon><n-icon><add-outline /></n-icon></template>
                    </n-button>
                  </div>
                </template>
                <div class="card-content compact-content">
                  <div class="security-display">
                    <div class="score-big" :style="{ color: getSecurityColor(securityScore) }">
                      {{ securityScore }}
                    </div>
                    <n-tag :type="getSecurityLevelType(securityScore)" :bordered="false" size="small">
                      {{ getSecurityLevelText(securityScore) }}
                    </n-tag>
                  </div>
                </div>
              </n-card>
            </div>
          </div>
        </div>
      </div>
    </n-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import {
  NSpin,
  NGrid,
  NGi,
  NCard,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NTag,
  NList,
  NListItem,
  NIcon,
  NEmpty,
  useMessage
} from 'naive-ui';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';
import {
  CheckmarkCircleOutline,
  CloseCircleOutline,
  AppsOutline,
  AddOutline
} from '@vicons/ionicons5';

const loading = ref(false);
const userStore = useUserStore();
const message = useMessage();

const recentApps = ref([]);

// 用于高度同步的refs
const mainCard = ref(null);
const sideCards = ref(null);



// 安全相关数据
const securityScore = ref(75); // 安全评分 0-100

const securityItems = computed(() => [
  {
    key: 'email_verified',
    name: '邮箱验证',
    status: userStore.user?.is_email_verified || false,
    actionText: '去验证'
  },
  {
    key: 'phone_verified',
    name: '手机验证',
    status: userStore.user?.is_phone_verified || false,
    actionText: '去验证'
  },
  {
    key: 'level2_verified',
    name: '实名认证',
    status: userStore.user?.level2_verified || false,
    actionText: '去认证'
  },
  {
    key: 'mfa_enabled',
    name: '双因子认证',
    status: userStore.user?.security_mfa_enabled || false,
    actionText: '去开启'
  }
]);

// 检查是否为深色模式
const isDarkMode = computed(() => {
  const themeMode = localStorage.getItem('theme') || 'system';
  if (themeMode === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return themeMode === 'dark';
});

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  // Using toLocaleString for a more standard format, customize as needed
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 安全相关方法
const getSecurityArcPath = (score) => {
  // 将分数转换为弧度 (0-100 映射到 0-π)
  const angle = (score / 100) * Math.PI;
  const x = 100 + 80 * Math.cos(Math.PI - angle);
  const y = 100 - 80 * Math.sin(Math.PI - angle);

  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;
};

const getSecurityColor = (score) => {
  if (score >= 80) return '#18a058'; // 绿色 - 安全
  if (score >= 60) return '#f0a020'; // 橙色 - 一般
  return '#d03050'; // 红色 - 危险
};

const getSecurityLevelType = (score) => {
  if (score >= 80) return 'success';
  if (score >= 60) return 'warning';
  return 'error';
};

const getSecurityLevelText = (score) => {
  if (score >= 80) return '安全';
  if (score >= 60) return '一般';
  return '危险';
};

// 紧凑型仪表盘弧线路径计算
const getCompactSecurityArcPath = (score) => {
  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸
  const angle = (score / 100) * Math.PI;
  const x = 60 + 45 * Math.cos(Math.PI - angle);
  const y = 65 - 45 * Math.sin(Math.PI - angle);

  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;
};

const handleSecurityAction = (key) => {
  switch (key) {
    case 'email_verified':
      message.info('邮箱验证功能开发中');
      break;
    case 'phone_verified':
      message.info('手机验证功能开发中');
      break;
    case 'level2_verified':
      window.location.href = '/verification';
      break;
    case 'mfa_enabled':
      window.location.href = '/security';
      break;
    default:
      message.info('功能开发中');
  }
};

// 计算安全评分
const calculateSecurityScore = () => {
  const items = securityItems.value;
  const completedItems = items.filter(item => item.status).length;
  const score = Math.round((completedItems / items.length) * 100);
  securityScore.value = score;
};

const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get('/dashboard');

    if (response.data && response.data.success) {
      // 更新用户信息，使用后端返回的格式化数据
      if (response.data.user) {
        userStore.user = {
          ...userStore.user,
          ...response.data.user,
          // 使用后端返回的格式化时间，如果没有则使用原始数据
          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,
          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,
          last_login: response.data.user.last_login,
          lastLoginIp: response.data.user.lastLoginIp,
          level2_verified: response.data.user.level2_verified
        };
      }

      // 更新应用列表
      recentApps.value = response.data.recentApps || [];

      console.log('仪表盘数据加载成功:', response.data);
    }

    // 计算安全评分
    calculateSecurityScore();
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 同步右侧卡片高度到左侧信息卡片高度
const syncCardHeights = () => {
  if (mainCard.value && sideCards.value) {
    // 获取左侧信息卡片的实际高度
    const mainCardHeight = mainCard.value.offsetHeight;

    // 设置右侧容器的高度等于左侧信息卡片的高度
    sideCards.value.style.height = `${mainCardHeight}px`;

    console.log('同步高度 - 左侧卡片:', mainCardHeight, 'px');
  }
};

onMounted(() => {
  // We need user data, if not present, maybe fetch it or rely on login flow
  if (!userStore.user) {
    // This case might happen on a page refresh, you might want to fetch user data
    // For now, we assume user data is populated from login
  }
  fetchDashboardData();

  // 等待DOM渲染完成后同步高度
  setTimeout(() => {
    syncCardHeights();
  }, 300);

  // 监听窗口大小变化，重新同步高度
  window.addEventListener('resize', syncCardHeights);
});
</script>
<style scoped>
.dashboard-container {
  padding: 16px;
  min-height: 100%;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.info-alert {
  margin-bottom: 16px;
  flex-shrink: 0;
}

/* 重构后的布局样式 */
.dashboard-main-layout {
  display: flex;
  gap: 16px;
  flex: 1;
  align-items: flex-start; /* 从顶部对齐，不拉伸 */
}

/* 完全重构的布局 - 精确控制高度 */
.cards-container {
  display: flex;
  gap: 16px;
  flex: 1;
  align-items: flex-start; /* 从顶部对齐，不拉伸 */
}

.main-card {
  flex: 2; /* 占2/3宽度 */
}

.side-cards {
  flex: 1; /* 占1/3宽度 */
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少卡片间距 */
  /* 高度将由JavaScript动态设置 */
}

.side-card {
  /* 让两个卡片等高 */
  flex: 1; /* 平分右侧区域高度 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 卡片基础样式 */
.main-card .n-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.side-card .n-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: 100%; /* 让卡片填充整个容器高度 */
  display: flex;
  flex-direction: column;
}

/* 确保卡片内容区域也是flex布局 */
.side-card .n-card .n-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0; /* 移除默认padding */
}

/* 确保卡片头部不占用过多空间 */
.side-card .n-card .n-card__header {
  padding: 12px 16px 8px 16px; /* 减少头部padding */
  flex-shrink: 0; /* 不允许收缩 */
}

/* 紧凑的卡片内容样式 */
.compact-content {
  padding: 0; /* 移除padding让内容完全居中 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
  overflow: hidden; /* 防止内容溢出 */
  flex: 1; /* 填充整个卡片内容区域 */
  height: 100%; /* 确保占满整个卡片高度 */
  min-height: 150px; /* 增加最小高度确保有足够空间居中 */
  position: relative; /* 为绝对定位做准备 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  padding: 0; /* 移除额外padding */
  margin: 0; /* 移除额外margin */
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #18a058;
  flex-shrink: 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #d0d0d0;
  font-size: 14px;
}

/* 卡片内容样式 */
.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  padding: 16px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

/* 安全指数样式 */
.security-display {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 1;
  height: 100%;
  width: 100%;
  padding: 0; /* 移除padding，让内容完全居中 */
}

.score-big {
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  margin: 0;
  text-align: center;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}



.service-card-wrapper,
.security-card-wrapper {
  flex: 1; /* 两个卡片平分右侧高度 */
  display: flex;
  flex-direction: column;
}

.service-card-new,
.security-card-new {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 移除高度限制，让它们平分父容器高度 */
}

/* 服务卡片内容样式 */
.service-content-new {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 12px 0;
  min-height: 80px;
}

.service-list-new {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.service-item-new {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-dot-new {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #18a058;
  flex-shrink: 0;
}

.service-name-new {
  font-size: 14px;
  color: var(--text-color);
}

.no-services-new {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #d0d0d0;
  font-size: 14px;
}

/* 安全指数卡片样式 */
.security-header-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.security-action-btn-new {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.security-action-btn-new:hover {
  opacity: 1;
}

.security-content-new {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
  padding: 12px 0;
  min-height: 80px;
}

.security-score-new {
  text-align: center;
}

.score-number-new {
  font-size: clamp(28px, 6vw, 48px);
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.score-label-new {
  margin-bottom: 16px;
}

.security-guide-new {
  text-align: center;
}

.guide-text-new {
  font-size: 12px;
  color: var(--text-color-3);
  display: block;
  margin-bottom: 8px;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 垂直并排布局 */
.side-cards-horizontal {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  flex: 1;
}

.compact-card {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

/* 授权服务卡片样式 */
.services-card {
  min-width: 0; /* 允许内容收缩，适应水平布局 */
}

.services-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1; /* 填充整个卡片高度 */
  padding: 8px 0;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 增加项目间距 */
}

.service-item-compact {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px; /* 增加内边距 */
  background-color: var(--n-color-target);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.service-item-compact:hover {
  background-color: var(--n-color-target-hover);
}

.service-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--n-primary-color);
  flex-shrink: 0;
}

.service-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--n-text-color-3);
  font-size: 13px;
  padding: 20px;
}

.more-services {
  font-size: 12px;
  color: var(--n-text-color-3);
  text-align: center;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: var(--n-color-target);
  border-radius: 4px;
}

.right-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.service-item {
  padding: 8px 4px !important;
  transition: color 0.3s;
}

.service-item:hover {
  color: var(--n-primary-color);
}

/* 安全指标卡片样式 */
.security-card {
  min-height: 240px;
  overflow: visible;
}

.security-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.security-action-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.security-action-btn:hover {
  opacity: 1;
}

.security-index-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  padding: 16px;
  justify-content: flex-start;
}

.security-score-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.score-number {
  font-size: clamp(32px, 8vw, 48px);
  font-weight: 700;
  line-height: 1;
  transition: color 0.3s ease;
}

.score-label {
  display: flex;
  justify-content: center;
}

.security-guide {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
  text-align: center;
}

.guide-text {
  font-size: 13px;
  color: var(--n-text-color-2);
  line-height: 1.4;
}

/* 通用样式 */
.security-arc {
  transition: all 0.3s ease;
}

/* 确保网格布局紧凑 */
:deep(.n-grid) {
  height: 100%;
}

:deep(.n-gi) {
  height: fit-content;
}

/* 减少卡片内部间距 */
:deep(.n-card .n-card__content) {
  padding: 16px;
}

:deep(.n-descriptions) {
  margin-bottom: 0;
}

:deep(.n-descriptions-item) {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .side-cards {
  gap: 24px;
  padding: 8px;
}

  .gauge-svg-compact {
    max-width: 100px;
  }

  .gauge-score-compact {
    font-size: 16px;
  }

  .service-name,
  .item-name-compact {
    font-size: 12px;
  }
}
</style>