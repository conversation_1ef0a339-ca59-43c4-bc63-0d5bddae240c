{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, resolveDynamicComponent as _resolveDynamicComponent, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"applications-container\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"applications-grid\"\n};\nconst _hoisted_4 = {\n  class: \"app-cover\"\n};\nconst _hoisted_5 = {\n  class: \"app-description\"\n};\nconst _hoisted_6 = {\n  class: \"app-meta\"\n};\nconst _hoisted_7 = {\n  class: \"app-created\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_empty = _resolveComponent(\"n-empty\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_tag = _resolveComponent(\"n-tag\");\n  const _component_n_card = _resolveComponent(\"n-card\");\n  const _component_n_spin = _resolveComponent(\"n-spin\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"h2\", {\n    class: \"page-title\"\n  }, \"我的应用\", -1 /* CACHED */)), _createVNode(_component_n_button, {\n    type: \"primary\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/applications/create'))\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 创建应用 \")])),\n    _: 1 /* STABLE */,\n    __: [2]\n  })]), _createVNode(_component_n_card, {\n    class: \"applications-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_n_spin, {\n      show: _ctx.loading\n    }, {\n      default: _withCtx(() => [_ctx.applications.length === 0 ? (_openBlock(), _createBlock(_component_n_empty, {\n        key: 0,\n        description: \"暂无应用\"\n      }, {\n        extra: _withCtx(() => [_createVNode(_component_n_button, {\n          type: \"primary\",\n          onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/applications/create'))\n        }, {\n          default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 创建第一个应用 \")])),\n          _: 1 /* STABLE */,\n          __: [4]\n        })]),\n        _: 1 /* STABLE */\n      })) : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.applications, app => {\n        return _openBlock(), _createBlock(_component_n_card, {\n          key: app.id,\n          class: \"application-item\",\n          title: app.name,\n          size: \"small\",\n          hoverable: \"\",\n          onClick: $event => _ctx.handleViewApp(app.id)\n        }, {\n          cover: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_n_icon, {\n            size: \"48\",\n            color: app.icon_color || '#2080f0'\n          }, {\n            default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.getAppIcon(app.icon))))]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])])]),\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(app.description || '暂无描述'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, \" 创建时间: \" + _toDisplayString(_ctx.formatDate(app.createdAt)), 1 /* TEXT */), _createVNode(_component_n_tag, {\n            type: _ctx.getStatusType(app.status),\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.getStatusText(app.status)), 1 /* TEXT */)]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])])]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"title\", \"onClick\"]);\n      }), 128 /* KEYED_FRAGMENT */))]))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"show\"])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_n_button", "type", "onClick", "_cache", "$event", "_ctx", "$router", "push", "_component_n_card", "_component_n_spin", "show", "loading", "applications", "length", "_createBlock", "_component_n_empty", "description", "extra", "_withCtx", "_hoisted_3", "_Fragment", "_renderList", "app", "key", "id", "title", "name", "size", "hoverable", "handleViewApp", "cover", "_hoisted_4", "_component_n_icon", "color", "icon_color", "_resolveDynamicComponent", "getAppIcon", "icon", "_hoisted_5", "_toDisplayString", "_hoisted_6", "_hoisted_7", "formatDate", "createdAt", "_component_n_tag", "getStatusType", "status", "getStatusText"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Applications.vue"], "sourcesContent": ["<template>\r\n  <div class=\"applications-container\">\r\n    <div class=\"page-header\">\r\n      <h2 class=\"page-title\">我的应用</h2>\r\n      <n-button type=\"primary\" @click=\"$router.push('/applications/create')\">\r\n        创建应用\r\n      </n-button>\r\n    </div>\r\n    \r\n    <n-card class=\"applications-card\">\r\n      <n-spin :show=\"loading\">\r\n        <n-empty v-if=\"applications.length === 0\" description=\"暂无应用\">\r\n          <template #extra>\r\n            <n-button type=\"primary\" @click=\"$router.push('/applications/create')\">\r\n              创建第一个应用\r\n            </n-button>\r\n          </template>\r\n        </n-empty>\r\n        \r\n        <div v-else class=\"applications-grid\">\r\n          <n-card\r\n            v-for=\"app in applications\"\r\n            :key=\"app.id\"\r\n            class=\"application-item\"\r\n            :title=\"app.name\"\r\n            size=\"small\"\r\n            hoverable\r\n            @click=\"handleViewApp(app.id)\"\r\n          >\r\n            <template #cover>\r\n              <div class=\"app-cover\">\r\n                <n-icon size=\"48\" :color=\"app.icon_color || '#2080f0'\">\r\n                  <component :is=\"getAppIcon(app.icon)\" />\r\n                </n-icon>\r\n              </div>\r\n            </template>\r\n            \r\n            <div class=\"app-description\">\r\n              {{ app.description || '暂无描述' }}\r\n            </div>\r\n            \r\n            <div class=\"app-meta\">\r\n              <div class=\"app-created\">\r\n                创建时间: {{ formatDate(app.createdAt) }}\r\n              </div>\r\n              <n-tag :type=\"getStatusType(app.status)\" size=\"small\">\r\n                {{ getStatusText(app.status) }}\r\n              </n-tag>\r\n            </div>\r\n          </n-card>\r\n        </div>\r\n      </n-spin>\r\n    </n-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref, onMounted } from 'vue'\r\nimport { \r\n  NCard, \r\n  NButton, \r\n  NSpin,\r\n  NEmpty,\r\n  NTag,\r\n  NIcon,\r\n  useMessage\r\n} from 'naive-ui'\r\nimport { \r\n  Apps, \r\n  Briefcase, \r\n  Chatbubbles, \r\n  Cloud,\r\n  Desktop,\r\n  Globe,\r\n  Grid,\r\n  Layers,\r\n  PieChart,\r\n  Server\r\n} from '@vicons/ionicons5'\r\nimport axios from 'axios'\r\n\r\nexport default defineComponent({\r\n  name: 'ApplicationsPage',\r\n  components: {\r\n    NCard,\r\n    NButton,\r\n    NSpin,\r\n    NEmpty,\r\n    NTag,\r\n    NIcon\r\n  },\r\n  setup() {\r\n    const message = useMessage()\r\n    const loading = ref(false)\r\n    const applications = ref([])\r\n    \r\n    // 图标映射\r\n    const iconMap = {\r\n      'apps': Apps,\r\n      'briefcase': Briefcase,\r\n      'chat': Chatbubbles,\r\n      'cloud': Cloud,\r\n      'desktop': Desktop,\r\n      'globe': Globe,\r\n      'grid': Grid,\r\n      'layers': Layers,\r\n      'chart': PieChart,\r\n      'server': Server\r\n    }\r\n    \r\n    // 获取应用图标\r\n    const getAppIcon = (iconName) => {\r\n      return iconMap[iconName] || Apps\r\n    }\r\n    \r\n    // 获取状态类型\r\n    const getStatusType = (status) => {\r\n      const types = {\r\n        'active': 'success',\r\n        'pending': 'warning',\r\n        'disabled': 'error'\r\n      }\r\n      return types[status] || 'default'\r\n    }\r\n    \r\n    // 获取状态文本\r\n    const getStatusText = (status) => {\r\n      const texts = {\r\n        'active': '已启用',\r\n        'pending': '审核中',\r\n        'disabled': '已禁用'\r\n      }\r\n      return texts[status] || '未知'\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n    \r\n    // 查看应用详情\r\n    const handleViewApp = (appId) => {\r\n      if (!appId) return\r\n      window.location.href = `/applications/${appId}`\r\n    }\r\n    \r\n    // 加载应用列表\r\n    const loadApplications = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get('/oauth/clients')\r\n        applications.value = response.data.clients || []\r\n      } catch (error) {\r\n        message.error('获取应用列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    onMounted(() => {\r\n      loadApplications()\r\n    })\r\n    \r\n    return {\r\n      loading,\r\n      applications,\r\n      getAppIcon,\r\n      getStatusType,\r\n      getStatusText,\r\n      formatDate,\r\n      handleViewApp,\r\n      Apps,\r\n      Briefcase,\r\n      Chatbubbles,\r\n      Cloud,\r\n      Desktop,\r\n      Globe,\r\n      Grid,\r\n      Layers,\r\n      PieChart,\r\n      Server\r\n    }\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.applications-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.page-title {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n.applications-card {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.applications-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.application-item {\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.application-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.app-cover {\r\n  height: 100px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.app-description {\r\n  margin: 12px 0;\r\n  height: 40px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  color: rgba(0, 0, 0, 0.6);\r\n  font-size: 14px;\r\n}\r\n\r\n.app-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 12px;\r\n  font-size: 12px;\r\n  color: rgba(0, 0, 0, 0.45);\r\n}\r\n</style> "], "mappings": ";;;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAa;;;EAiBRA,KAAK,EAAC;;;EAWPA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAiB;;EAIvBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;;;;;;;uBAzCpCC,mBAAA,CAoDM,OApDNC,UAoDM,GAnDJC,mBAAA,CAKM,OALNC,UAKM,G,0BAJJD,mBAAA,CAAgC;IAA5BH,KAAK,EAAC;EAAY,GAAC,MAAI,qBAC3BK,YAAA,CAEWC,mBAAA;IAFDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;sBAA0B,MAEvEJ,MAAA,QAAAA,MAAA,O,iBAFuE,QAEvE,E;;;QAGFJ,YAAA,CA2CSS,iBAAA;IA3CDd,KAAK,EAAC;EAAmB;sBAC/B,MAyCS,CAzCTK,YAAA,CAyCSU,iBAAA;MAzCAC,IAAI,EAAEL,IAAA,CAAAM;IAAO;wBAMlB,MAmBM,CAxBON,IAAA,CAAAO,YAAY,CAACC,MAAM,U,cAAlCC,YAAA,CAMUC,kBAAA;;QANgCC,WAAW,EAAC;;QACzCC,KAAK,EAAAC,QAAA,CACd,MAEW,CAFXnB,YAAA,CAEWC,mBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;4BAA0B,MAEvEJ,MAAA,QAAAA,MAAA,O,iBAFuE,WAEvE,E;;;;;2BAIJR,mBAAA,CA+BM,OA/BNwB,UA+BM,I,kBA9BJxB,mBAAA,CA6BSyB,SAAA,QAAAC,WAAA,CA5BOhB,IAAA,CAAAO,YAAY,EAAnBU,GAAG;6BADZR,YAAA,CA6BSN,iBAAA;UA3BNe,GAAG,EAAED,GAAG,CAACE,EAAE;UACZ9B,KAAK,EAAC,kBAAkB;UACvB+B,KAAK,EAAEH,GAAG,CAACI,IAAI;UAChBC,IAAI,EAAC,OAAO;UACZC,SAAS,EAAT,EAAS;UACR1B,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAwB,aAAa,CAACP,GAAG,CAACE,EAAE;;UAEjBM,KAAK,EAAAZ,QAAA,CACd,MAIM,CAJNrB,mBAAA,CAIM,OAJNkC,UAIM,GAHJhC,YAAA,CAESiC,iBAAA;YAFDL,IAAI,EAAC,IAAI;YAAEM,KAAK,EAAEX,GAAG,CAACY,UAAU;;8BACtC,MAAwC,E,cAAxCpB,YAAA,CAAwCqB,wBAAA,CAAxB9B,IAAA,CAAA+B,UAAU,CAACd,GAAG,CAACe,IAAI,K;;;4BAKzC,MAEM,CAFNxC,mBAAA,CAEM,OAFNyC,UAEM,EAAAC,gBAAA,CADDjB,GAAG,CAACN,WAAW,4BAGpBnB,mBAAA,CAOM,OAPN2C,UAOM,GANJ3C,mBAAA,CAEM,OAFN4C,UAEM,EAFmB,SACjB,GAAAF,gBAAA,CAAGlC,IAAA,CAAAqC,UAAU,CAACpB,GAAG,CAACqB,SAAS,mBAEnC5C,YAAA,CAEQ6C,gBAAA;YAFA3C,IAAI,EAAEI,IAAA,CAAAwC,aAAa,CAACvB,GAAG,CAACwB,MAAM;YAAGnB,IAAI,EAAC;;8BAC5C,MAA+B,C,kCAA5BtB,IAAA,CAAA0C,aAAa,CAACzB,GAAG,CAACwB,MAAM,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}